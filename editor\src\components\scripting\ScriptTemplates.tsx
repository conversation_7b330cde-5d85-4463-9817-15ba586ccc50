/**
 * 脚本模板管理器
 * 提供预定义的脚本模板
 */
import React, { useState } from 'react';
import { 
  Modal, 
  List, 
  Card, 
  Button, 
  Input, 
  Space, 
  Tag, 
  Typography,
  Divider,
  message
} from 'antd';
import { 
  CodeOutlined, 
  ApartmentOutlined, 
  SearchOutlined,
  EyeOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Search } = Input;
const { Text, Paragraph } = Typography;

/**
 * 脚本模板接口
 */
interface ScriptTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  type: 'javascript' | 'typescript' | 'visual_script';
  tags: string[];
  content: string;
  preview?: string;
  author?: string;
  version?: string;
}

/**
 * 脚本模板属性
 */
interface ScriptTemplatesProps {
  /** 是否显示模态框 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 选择模板回调 */
  onSelectTemplate: (template: ScriptTemplate) => void;
}

/**
 * 脚本模板管理器组件
 */
const ScriptTemplates: React.FC<ScriptTemplatesProps> = ({
  visible,
  onClose,
  onSelectTemplate
}) => {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [previewTemplate, setPreviewTemplate] = useState<ScriptTemplate | null>(null);

  // 预定义模板
  const templates: ScriptTemplate[] = [
    {
      id: 'basic-js',
      name: '基础JavaScript脚本',
      description: '包含基本生命周期函数的JavaScript脚本模板',
      category: 'basic',
      type: 'javascript',
      tags: ['基础', '生命周期'],
      content: `// 基础JavaScript脚本
// 在这里编写您的脚本代码

// 生命周期函数
function onStart() {
    console.log('脚本开始执行');
    // 初始化代码
}

function onUpdate(deltaTime) {
    // 每帧更新代码
}

function onDestroy() {
    console.log('脚本销毁');
    // 清理代码
}

// 导出生命周期函数
export { onStart, onUpdate, onDestroy };`,
      author: 'DL Engine',
      version: '1.0.0'
    },
    {
      id: 'basic-ts',
      name: '基础TypeScript脚本',
      description: '包含类型定义的TypeScript脚本模板',
      category: 'basic',
      type: 'typescript',
      tags: ['基础', 'TypeScript', '类型安全'],
      content: `// 基础TypeScript脚本
import { Entity, Component } from 'dl-engine';

// 脚本配置接口
interface ScriptConfig {
    enabled: boolean;
    debug: boolean;
}

// 默认配置
const config: ScriptConfig = {
    enabled: true,
    debug: false
};

// 生命周期函数
export function onStart(): void {
    if (config.debug) {
        console.log('脚本开始执行');
    }
    // 初始化代码
}

export function onUpdate(deltaTime: number): void {
    if (!config.enabled) return;
    // 每帧更新代码
}

export function onDestroy(): void {
    if (config.debug) {
        console.log('脚本销毁');
    }
    // 清理代码
}`,
      author: 'DL Engine',
      version: '1.0.0'
    },
    {
      id: 'player-controller',
      name: '玩家控制器',
      description: '处理玩家输入和移动的脚本',
      category: 'gameplay',
      type: 'javascript',
      tags: ['玩家', '控制', '输入', '移动'],
      content: `// 玩家控制器脚本
import { InputSystem, Vector3 } from 'dl-engine';

let inputSystem = null;
let transform = null;
let moveSpeed = 5.0;
let rotateSpeed = 90.0;

function onStart() {
    // 获取输入系统
    inputSystem = engine.getSystem('InputSystem');
    
    // 获取变换组件
    transform = entity.getTransform();
    
    console.log('玩家控制器初始化完成');
}

function onUpdate(deltaTime) {
    if (!inputSystem || !transform) return;
    
    // 获取输入
    const moveX = inputSystem.getAxis('Horizontal');
    const moveZ = inputSystem.getAxis('Vertical');
    const rotate = inputSystem.getAxis('Mouse X');
    
    // 移动
    if (moveX !== 0 || moveZ !== 0) {
        const moveDirection = new Vector3(moveX, 0, moveZ).normalize();
        const moveDistance = moveSpeed * deltaTime;
        transform.translate(moveDirection.multiplyScalar(moveDistance));
    }
    
    // 旋转
    if (rotate !== 0) {
        const rotateAmount = rotate * rotateSpeed * deltaTime;
        transform.rotateY(rotateAmount * Math.PI / 180);
    }
}

export { onStart, onUpdate };`,
      author: 'DL Engine',
      version: '1.0.0'
    },
    {
      id: 'ui-manager',
      name: 'UI管理器',
      description: '管理用户界面元素的脚本',
      category: 'ui',
      type: 'typescript',
      tags: ['UI', '界面', '管理'],
      content: `// UI管理器脚本
import { UISystem, Entity } from 'dl-engine';

interface UIElement {
    id: string;
    element: any;
    visible: boolean;
}

class UIManager {
    private uiSystem: UISystem | null = null;
    private elements: Map<string, UIElement> = new Map();
    
    public initialize(): void {
        this.uiSystem = engine.getSystem('UISystem') as UISystem;
        this.setupUI();
    }
    
    private setupUI(): void {
        // 创建主菜单
        this.createElement('mainMenu', 'Panel', {
            position: { x: 0, y: 0 },
            size: { width: 200, height: 300 },
            visible: true
        });
        
        // 创建暂停菜单
        this.createElement('pauseMenu', 'Panel', {
            position: { x: 0, y: 0 },
            size: { width: 300, height: 200 },
            visible: false
        });
    }
    
    public createElement(id: string, type: string, options: any): void {
        if (!this.uiSystem) return;
        
        const element = this.uiSystem.createUI(type, options);
        this.elements.set(id, {
            id,
            element,
            visible: options.visible || false
        });
    }
    
    public showElement(id: string): void {
        const uiElement = this.elements.get(id);
        if (uiElement) {
            uiElement.visible = true;
            // 显示UI元素的逻辑
        }
    }
    
    public hideElement(id: string): void {
        const uiElement = this.elements.get(id);
        if (uiElement) {
            uiElement.visible = false;
            // 隐藏UI元素的逻辑
        }
    }
}

const uiManager = new UIManager();

export function onStart(): void {
    uiManager.initialize();
}

export { uiManager };`,
      author: 'DL Engine',
      version: '1.0.0'
    }
  ];

  // 分类列表
  const categories = [
    { key: 'all', label: '全部' },
    { key: 'basic', label: '基础' },
    { key: 'gameplay', label: '游戏玩法' },
    { key: 'ui', label: '用户界面' },
    { key: 'physics', label: '物理' },
    { key: 'animation', label: '动画' },
    { key: 'audio', label: '音频' }
  ];

  // 过滤模板
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = searchText === '' || 
      template.name.toLowerCase().includes(searchText.toLowerCase()) ||
      template.description.toLowerCase().includes(searchText.toLowerCase()) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchText.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // 处理模板选择
  const handleSelectTemplate = (template: ScriptTemplate) => {
    onSelectTemplate(template);
    onClose();
    message.success(`已选择模板: ${template.name}`);
  };

  // 处理模板预览
  const handlePreviewTemplate = (template: ScriptTemplate) => {
    setPreviewTemplate(template);
  };

  // 渲染模板卡片
  const renderTemplateCard = (template: ScriptTemplate) => (
    <Card
      key={template.id}
      size="small"
      title={
        <Space>
          {template.type === 'visual_script' ? <ApartmentOutlined /> : <CodeOutlined />}
          {template.name}
        </Space>
      }
      extra={
        <Space>
          <Button 
            type="text" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => handlePreviewTemplate(template)}
          />
          <Button 
            type="primary" 
            size="small" 
            icon={<DownloadOutlined />}
            onClick={() => handleSelectTemplate(template)}
          >
            使用
          </Button>
        </Space>
      }
      style={{ marginBottom: 16 }}
    >
      <Paragraph ellipsis={{ rows: 2 }}>
        {template.description}
      </Paragraph>
      
      <Space wrap>
        {template.tags.map(tag => (
          <Tag key={tag} size="small">{tag}</Tag>
        ))}
      </Space>
      
      {template.author && (
        <div style={{ marginTop: 8 }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            作者: {template.author} | 版本: {template.version}
          </Text>
        </div>
      )}
    </Card>
  );

  return (
    <>
      <Modal
        title="选择脚本模板"
        open={visible}
        onCancel={onClose}
        width={800}
        footer={null}
      >
        <div style={{ marginBottom: 16 }}>
          <Search
            placeholder="搜索模板..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ marginBottom: 16 }}
          />
          
          <Space wrap>
            {categories.map(category => (
              <Button
                key={category.key}
                type={selectedCategory === category.key ? 'primary' : 'default'}
                size="small"
                onClick={() => setSelectedCategory(category.key)}
              >
                {category.label}
              </Button>
            ))}
          </Space>
        </div>
        
        <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
          {filteredTemplates.map(renderTemplateCard)}
          
          {filteredTemplates.length === 0 && (
            <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
              没有找到匹配的模板
            </div>
          )}
        </div>
      </Modal>
      
      {/* 模板预览模态框 */}
      <Modal
        title={`预览: ${previewTemplate?.name}`}
        open={!!previewTemplate}
        onCancel={() => setPreviewTemplate(null)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setPreviewTemplate(null)}>
            关闭
          </Button>,
          <Button 
            key="use" 
            type="primary" 
            onClick={() => previewTemplate && handleSelectTemplate(previewTemplate)}
          >
            使用此模板
          </Button>
        ]}
      >
        {previewTemplate && (
          <div>
            <Paragraph>{previewTemplate.description}</Paragraph>
            <Divider />
            <pre style={{ 
              backgroundColor: '#f5f5f5', 
              padding: '16px', 
              borderRadius: '4px',
              fontSize: '12px',
              maxHeight: '300px',
              overflow: 'auto'
            }}>
              {previewTemplate.content}
            </pre>
          </div>
        )}
      </Modal>
    </>
  );
};

export default ScriptTemplates;
