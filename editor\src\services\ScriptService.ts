/**
 * 脚本服务
 * 管理脚本的执行、编译和与底层引擎的集成
 */
import EngineService from './EngineService';

/**
 * 脚本类型枚举
 */
export enum ScriptType {
  JAVASCRIPT = 'javascript',
  TYPESCRIPT = 'typescript',
  VISUAL_SCRIPT = 'visual_script'
}

/**
 * 脚本执行状态
 */
export enum ScriptExecutionState {
  STOPPED = 'stopped',
  RUNNING = 'running',
  PAUSED = 'paused',
  ERROR = 'error'
}

/**
 * 脚本数据接口
 */
export interface ScriptData {
  id: string;
  name: string;
  type: ScriptType;
  content: string;
  enabled: boolean;
  autoRun: boolean;
  domain: string;
  entityId?: string;
  visualScript?: any;
  variables?: Record<string, any>;
  lastModified: Date;
}

/**
 * 脚本执行上下文
 */
export interface ScriptExecutionContext {
  entity?: any;
  engine?: any;
  deltaTime?: number;
  [key: string]: any;
}

/**
 * 脚本错误信息
 */
export interface ScriptError {
  message: string;
  line?: number;
  column?: number;
  stack?: string;
}

/**
 * 脚本服务类
 */
class ScriptService {
  private static instance: ScriptService;
  private engineService: typeof EngineService;
  private scripts: Map<string, ScriptData> = new Map();
  private executionStates: Map<string, ScriptExecutionState> = new Map();
  private compiledScripts: Map<string, Function> = new Map();
  private scriptContexts: Map<string, ScriptExecutionContext> = new Map();

  constructor() {
    this.engineService = EngineService;
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): ScriptService {
    if (!ScriptService.instance) {
      ScriptService.instance = new ScriptService();
    }
    return ScriptService.instance;
  }

  /**
   * 注册脚本
   */
  public registerScript(scriptData: ScriptData): void {
    this.scripts.set(scriptData.id, scriptData);
    this.executionStates.set(scriptData.id, ScriptExecutionState.STOPPED);
    
    // 如果启用自动运行，则编译脚本
    if (scriptData.autoRun && scriptData.enabled) {
      this.compileScript(scriptData.id);
    }
  }

  /**
   * 获取脚本
   */
  public getScript(scriptId: string): ScriptData | undefined {
    return this.scripts.get(scriptId);
  }

  /**
   * 更新脚本
   */
  public updateScript(scriptId: string, updates: Partial<ScriptData>): void {
    const script = this.scripts.get(scriptId);
    if (script) {
      const updatedScript = { ...script, ...updates, lastModified: new Date() };
      this.scripts.set(scriptId, updatedScript);
      
      // 如果内容发生变化，重新编译
      if (updates.content) {
        this.compileScript(scriptId);
      }
    }
  }

  /**
   * 删除脚本
   */
  public removeScript(scriptId: string): void {
    this.stopScript(scriptId);
    this.scripts.delete(scriptId);
    this.executionStates.delete(scriptId);
    this.compiledScripts.delete(scriptId);
    this.scriptContexts.delete(scriptId);
  }

  /**
   * 编译脚本
   */
  public async compileScript(scriptId: string): Promise<boolean> {
    const script = this.scripts.get(scriptId);
    if (!script) {
      console.error(`脚本不存在: ${scriptId}`);
      return false;
    }

    try {
      if (script.type === ScriptType.VISUAL_SCRIPT) {
        // 处理可视化脚本
        return this.compileVisualScript(script);
      } else {
        // 处理代码脚本
        return this.compileCodeScript(script);
      }
    } catch (error) {
      console.error(`脚本编译失败: ${scriptId}`, error);
      this.executionStates.set(scriptId, ScriptExecutionState.ERROR);
      return false;
    }
  }

  /**
   * 编译代码脚本
   */
  private compileCodeScript(script: ScriptData): boolean {
    try {
      // 创建脚本执行环境
      const scriptFunction = new Function(
        'entity', 
        'engine', 
        'console', 
        'deltaTime',
        script.content
      );
      
      this.compiledScripts.set(script.id, scriptFunction);
      return true;
    } catch (error) {
      console.error(`代码脚本编译失败: ${script.id}`, error);
      return false;
    }
  }

  /**
   * 编译可视化脚本
   */
  private compileVisualScript(script: ScriptData): boolean {
    try {
      if (!script.visualScript) {
        return false;
      }

      // 使用底层引擎的可视化脚本系统
      const visualScriptEngine = this.engineService.getVisualScriptEngine();
      if (visualScriptEngine) {
        const compiledScript = visualScriptEngine.compile(script.visualScript);
        this.compiledScripts.set(script.id, compiledScript);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error(`可视化脚本编译失败: ${script.id}`, error);
      return false;
    }
  }

  /**
   * 执行脚本
   */
  public async executeScript(scriptId: string, context?: ScriptExecutionContext): Promise<boolean> {
    const script = this.scripts.get(scriptId);
    const compiledScript = this.compiledScripts.get(scriptId);
    
    if (!script || !compiledScript) {
      console.error(`脚本或编译结果不存在: ${scriptId}`);
      return false;
    }

    if (!script.enabled) {
      console.warn(`脚本未启用: ${scriptId}`);
      return false;
    }

    try {
      this.executionStates.set(scriptId, ScriptExecutionState.RUNNING);
      
      // 创建执行上下文
      const executionContext = {
        entity: context?.entity || this.getEntityForScript(script),
        engine: this.engineService,
        console: console,
        deltaTime: context?.deltaTime || 0,
        ...context
      };
      
      this.scriptContexts.set(scriptId, executionContext);

      if (script.type === ScriptType.VISUAL_SCRIPT) {
        // 执行可视化脚本
        await this.executeVisualScript(script, executionContext);
      } else {
        // 执行代码脚本
        await this.executeCodeScript(script, compiledScript, executionContext);
      }

      return true;
    } catch (error) {
      console.error(`脚本执行失败: ${scriptId}`, error);
      this.executionStates.set(scriptId, ScriptExecutionState.ERROR);
      return false;
    }
  }

  /**
   * 执行代码脚本
   */
  private async executeCodeScript(
    script: ScriptData, 
    compiledScript: Function, 
    context: ScriptExecutionContext
  ): Promise<void> {
    try {
      await compiledScript.call(
        null,
        context.entity,
        context.engine,
        context.console,
        context.deltaTime
      );
    } catch (error) {
      throw new Error(`代码脚本执行错误: ${error.message}`);
    }
  }

  /**
   * 执行可视化脚本
   */
  private async executeVisualScript(
    script: ScriptData, 
    context: ScriptExecutionContext
  ): Promise<void> {
    try {
      const visualScriptEngine = this.engineService.getVisualScriptEngine();
      if (visualScriptEngine && script.visualScript) {
        await visualScriptEngine.execute(script.visualScript, context);
      }
    } catch (error) {
      throw new Error(`可视化脚本执行错误: ${error.message}`);
    }
  }

  /**
   * 停止脚本
   */
  public stopScript(scriptId: string): void {
    this.executionStates.set(scriptId, ScriptExecutionState.STOPPED);
    this.scriptContexts.delete(scriptId);
  }

  /**
   * 暂停脚本
   */
  public pauseScript(scriptId: string): void {
    this.executionStates.set(scriptId, ScriptExecutionState.PAUSED);
  }

  /**
   * 恢复脚本
   */
  public resumeScript(scriptId: string): void {
    this.executionStates.set(scriptId, ScriptExecutionState.RUNNING);
  }

  /**
   * 获取脚本执行状态
   */
  public getExecutionState(scriptId: string): ScriptExecutionState {
    return this.executionStates.get(scriptId) || ScriptExecutionState.STOPPED;
  }

  /**
   * 获取脚本对应的实体
   */
  private getEntityForScript(script: ScriptData): any {
    if (script.entityId) {
      return this.engineService.getEntity(script.entityId);
    }
    return null;
  }

  /**
   * 获取所有脚本
   */
  public getAllScripts(): ScriptData[] {
    return Array.from(this.scripts.values());
  }

  /**
   * 获取指定实体的脚本
   */
  public getScriptsForEntity(entityId: string): ScriptData[] {
    return this.getAllScripts().filter(script => script.entityId === entityId);
  }

  /**
   * 清理所有脚本
   */
  public cleanup(): void {
    // 停止所有运行中的脚本
    for (const scriptId of this.scripts.keys()) {
      this.stopScript(scriptId);
    }
    
    // 清理所有数据
    this.scripts.clear();
    this.executionStates.clear();
    this.compiledScripts.clear();
    this.scriptContexts.clear();
  }
}

export default ScriptService;
