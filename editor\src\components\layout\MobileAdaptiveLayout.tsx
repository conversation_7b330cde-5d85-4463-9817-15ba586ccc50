/**
 * 移动设备适配布局组件
 * 根据设备类型和屏幕方向自动切换布局
 */
import React, { useState, useEffect } from 'react';
import { Layout, Button, message, Modal, Spin, Badge } from 'antd';
import {
  SettingOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  MobileOutlined,
  TabletOutlined,
  DesktopOutlined,
  CloseOutlined,
  WarningOutlined,
  CompassOutlined,
  DashboardOutlined,
  WifiOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import { toggleFullscreen } from '../../store/ui/uiSlice';

import MobileDeviceService, { DeviceType, ScreenOrientation } from '../../services/MobileDeviceService';
import MobileLayoutService from '../../services/MobileLayoutService';
import MobilePerformanceService, { PerformanceLevel } from '../../services/MobilePerformanceService';
import MobileNetworkService, { NetworkQualityLevel } from '../../services/MobileNetworkService';
import ResponsiveDesignService, { ResponsiveBreakpoint, ControlSize } from '../../services/ResponsiveDesignService';
import MobileToolbar from '../mobile/MobileToolbar';
import TouchControlPanel from '../mobile/TouchControlPanel';
import GyroscopeControl from '../mobile/GyroscopeControl';
import './MobileAdaptiveLayout.less';

const { Content } = Layout;

interface MobileAdaptiveLayoutProps {
  children: React.ReactNode;
}

/**
 * 移动设备适配布局组件
 */
const MobileAdaptiveLayout: React.FC<MobileAdaptiveLayoutProps> = ({ children }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  // 从Redux获取状态
  const { fullscreen } = useAppSelector((state) => state.ui);

  // 本地状态
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [deviceType, setDeviceType] = useState(DeviceType.DESKTOP);
  const [orientation, setOrientation] = useState(ScreenOrientation.LANDSCAPE);

  const [controlPanelVisible, setControlPanelVisible] = useState(false);
  const [performanceLevel, setPerformanceLevel] = useState(PerformanceLevel.AUTO);
  const [networkQuality, setNetworkQuality] = useState(NetworkQualityLevel.GOOD);
  const [isLoading, setIsLoading] = useState(true);
  const [showPerformanceWarning, setShowPerformanceWarning] = useState(false);
  const [showNetworkWarning, setShowNetworkWarning] = useState(false);
  const [breakpoint, setBreakpoint] = useState<ResponsiveBreakpoint>(
    ResponsiveDesignService.getCurrentBreakpoint()
  );
  const [controlSize, setControlSize] = useState<ControlSize>(
    ResponsiveDesignService.getCurrentControlSize()
  );
  const [isTouchMode, setIsTouchMode] = useState<boolean>(
    ResponsiveDesignService.isTouchMode()
  );
  const [gyroscopeEnabled, setGyroscopeEnabled] = useState<boolean>(false);

  // 初始化
  useEffect(() => {
    // 获取设备信息
    const deviceInfo = MobileDeviceService.getDeviceInfo();
    setIsMobile(deviceInfo.isMobile);
    setIsTablet(deviceInfo.isTablet);
    setDeviceType(deviceInfo.type);
    setOrientation(deviceInfo.orientation);

    // 监听设备变化
    MobileDeviceService.on('deviceChanged', (info: any) => {
      setIsMobile(info.isMobile);
      setIsTablet(info.isTablet);
      setDeviceType(info.type);

      // 应用适当的布局
      applyAppropriateLayout(info.type, info.orientation);
    });

    MobileDeviceService.on('orientationChanged', (info: any) => {
      setOrientation(info.orientation);

      // 应用适当的布局
      applyAppropriateLayout(deviceType, info.orientation);
    });

    // 获取性能级别
    const perfLevel = MobilePerformanceService.getPerformanceLevel();
    setPerformanceLevel(perfLevel);

    // 监听性能变化
    MobilePerformanceService.on('performanceLevelChanged', (data: any) => {
      setPerformanceLevel(data.newLevel);
    });

    // 监听性能警告
    MobilePerformanceService.on('temperatureHigh', () => {
      setShowPerformanceWarning(true);
    });

    // 获取网络质量
    const networkStatus = MobileNetworkService.getNetworkStatus();
    setNetworkQuality(networkStatus.qualityLevel);

    // 监听网络变化
    MobileNetworkService.on('networkQualityChanged', (data: any) => {
      setNetworkQuality(data.newLevel);

      // 如果网络质量变差，显示警告
      if (data.newLevel === NetworkQualityLevel.POOR) {
        setShowNetworkWarning(true);
      }
    });

    // 监听响应式设计服务事件
    const handleBreakpointChange = (data: any) => {
      setBreakpoint(data.newBreakpoint);
    };

    const handleControlSizeChange = (data: any) => {
      setControlSize(data.newControlSize);
    };

    const handleTouchModeChange = (data: any) => {
      setIsTouchMode(data.newTouchMode);
    };

    ResponsiveDesignService.on('breakpointChanged', handleBreakpointChange);
    ResponsiveDesignService.on('controlSizeChanged', handleControlSizeChange);
    ResponsiveDesignService.on('touchModeChanged', handleTouchModeChange);

    // 应用适当的布局
    applyAppropriateLayout(deviceInfo.type, deviceInfo.orientation);

    // 模拟加载完成
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => {
      // 清理事件监听
      MobileDeviceService.removeAllListeners();
      MobilePerformanceService.removeAllListeners();
      MobileNetworkService.removeAllListeners();
      ResponsiveDesignService.off('breakpointChanged', handleBreakpointChange);
      ResponsiveDesignService.off('controlSizeChanged', handleControlSizeChange);
      ResponsiveDesignService.off('touchModeChanged', handleTouchModeChange);
    };
  }, []);

  // 应用适当的布局
  const applyAppropriateLayout = (type: DeviceType, orient: ScreenOrientation) => {
    if (type === DeviceType.DESKTOP) {
      // 桌面设备使用默认布局
      return;
    }

    // 根据设备类型和屏幕方向选择布局
    let layoutName = '';

    if (type === DeviceType.MOBILE) {
      if (orient === ScreenOrientation.PORTRAIT) {
        layoutName = 'mobilePortrait';
      } else {
        layoutName = 'mobileLandscape';
      }
    } else if (type === DeviceType.TABLET) {
      if (orient === ScreenOrientation.PORTRAIT) {
        layoutName = 'tabletPortrait';
      } else {
        layoutName = 'tabletLandscape';
      }
    }

    // 应用布局
    if (layoutName) {
      MobileLayoutService.applyMobileLayout(layoutName);
    }
  };

  // 处理陀螺仪启用状态变化
  const handleGyroscopeEnabledChange = (enabled: boolean) => {
    setGyroscopeEnabled(enabled);

    if (enabled) {
      message.success(t('gyroscope.enabledSuccess'));
    }
  };

  // 切换控制面板
  const toggleControlPanel = () => {
    setControlPanelVisible(!controlPanelVisible);
  };

  // 渲染移动设备警告
  const renderMobileWarning = () => {
    if (!isMobile && !isTablet) {
      return null;
    }

    return (
      <Modal
        title={
          <div className="warning-title">
            <WarningOutlined className="warning-icon" />
            {t('editor.mobile.warning')}
          </div>
        }
        visible={showPerformanceWarning || showNetworkWarning}
        onCancel={() => {
          setShowPerformanceWarning(false);
          setShowNetworkWarning(false);
        }}
        footer={[
          <Button key="ok" type="primary" onClick={() => {
            setShowPerformanceWarning(false);
            setShowNetworkWarning(false);
          }}>
            {t('editor.mobile.understand')}
          </Button>
        ]}
      >
        {showPerformanceWarning && (
          <div className="warning-message">
            <p>{t('editor.mobile.performanceWarning')}</p>
            <p>{t('editor.mobile.performanceWarningDetail')}</p>
          </div>
        )}

        {showNetworkWarning && (
          <div className="warning-message">
            <p>{t('editor.mobile.networkWarning')}</p>
            <p>{t('editor.mobile.networkWarningDetail')}</p>
          </div>
        )}
      </Modal>
    );
  };

  // 如果不是移动设备，直接渲染子组件
  if (!isMobile && !isTablet) {
    return <>{children}</>;
  }

  // 渲染加载状态
  if (isLoading) {
    return (
      <div className="mobile-loading">
        <Spin size="large" />
        <div className="loading-text">{t('editor.mobile.loading')}</div>
      </div>
    );
  }

  // 渲染移动设备布局
  return (
    <Layout className={`mobile-adaptive-layout ${orientation === ScreenOrientation.PORTRAIT ? 'portrait' : 'landscape'} breakpoint-${breakpoint.toLowerCase()} device-${deviceType.toLowerCase()} ${isTouchMode ? 'touch-mode' : ''}`}>
      <Content className="mobile-content">
        {children}

        {/* 移动工具栏 */}
        <MobileToolbar />

        {/* 触控控制面板 */}
        {controlPanelVisible && (
          <div className="touch-control-container">
            <TouchControlPanel />
          </div>
        )}

        {/* 陀螺仪控制 */}
        {deviceType !== DeviceType.DESKTOP && (
          <GyroscopeControl
            enabled={gyroscopeEnabled}
            onEnable={handleGyroscopeEnabledChange}
          />
        )}

        {/* 控制面板切换按钮 */}
        <Button
          className="control-panel-toggle"
          type="primary"
          shape="circle"
          size={controlSize === ControlSize.SMALL ? 'middle' : 'large'}
          icon={controlPanelVisible ? <CloseOutlined /> : <SettingOutlined />}
          onClick={toggleControlPanel}
        />

        {/* 全屏切换按钮 */}
        <Button
          className="fullscreen-toggle"
          type="primary"
          shape="circle"
          size={controlSize === ControlSize.SMALL ? 'middle' : 'large'}
          icon={fullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
          onClick={() => dispatch(toggleFullscreen())}
        />

        {/* 状态指示器 */}
        <div className="status-indicators">
          {/* 设备类型指示器 */}
          <Badge status={deviceType === DeviceType.MOBILE ? 'processing' : 'default'}>
            <div className="device-indicator">
              {deviceType === DeviceType.MOBILE ? (
                <MobileOutlined />
              ) : deviceType === DeviceType.TABLET ? (
                <TabletOutlined />
              ) : (
                <DesktopOutlined />
              )}
            </div>
          </Badge>

          {/* 性能指示器 */}
          <Badge status={performanceLevel === PerformanceLevel.LOW ? 'warning' : 'success'}>
            <div className="performance-indicator">
              <DashboardOutlined />
            </div>
          </Badge>

          {/* 网络指示器 */}
          <Badge status={networkQuality === NetworkQualityLevel.POOR ? 'error' : 'success'}>
            <div className="network-indicator">
              <WifiOutlined />
            </div>
          </Badge>

          {/* 陀螺仪指示器 */}
          {gyroscopeEnabled && (
            <Badge status="processing">
              <div className="gyroscope-indicator-icon">
                <CompassOutlined />
              </div>
            </Badge>
          )}
        </div>
      </Content>

      {/* 移动设备警告 */}
      {renderMobileWarning()}
    </Layout>
  );
};

export default MobileAdaptiveLayout;
